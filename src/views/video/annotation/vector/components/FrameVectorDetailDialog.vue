<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="90%"
    top="5vh"
    append-to-body
    :close-on-click-modal="false"
    class="el-dialog--beautify"
    @closed="handleClosed"
  >
    <EleSheet
      v-if="visible"
      ref="sheetRef"
      v-bind="sheetProps"
      class="frame-detail-sheet page-main page-main--flat"
    >

      <!-- 帧索引状态列自定义渲染 -->
      <template #table:value2:simple="{ row }">
        <el-image :src="row.value2" class="h-12 w-12" fit="contain" :preview-src-list="[row.value2]"></el-image>
      </template>
    </EleSheet>
  </el-dialog>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'FrameVectorDetailDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      currentRowData: null,
      dialogTitle: '帧向量明细'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '帧向量明细数据',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: 'frame_vector_detail_management',
                id: this.currentRowData?.value1
              }
            }),
          // add: (data) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'post',
          //     data: {
          //       ...data,
          //       type: 'frame_vector_detail_management'
          //     }
          //   }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        model: {
          // 图片
          value2: {
            type: 'text',
            label: '图片',
            align: 'center',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 帧唯一编号
          value3: {
            type: 'text',
            label: '帧唯一编号',
            align: 'left',
            width: 180,
            search: {
              placeholder: '请输入帧唯一编号'
            }
          },
          // 帧时间戳
          value4: {
            type: 'text',
            label: '帧时间戳',
            align: 'center',
            width: 160,
            search: {
              type: 'datetime',
              placeholder: '请选择帧时间戳'
            }
          },
          // 帧关键特征
          value5: {
            type: 'text',
            label: '帧关键特征',
            align: 'left',
            width: 200,
            search: {
              placeholder: '请输入帧关键特征'
            }
          },
          // 帧索引状态
          value6: {
            type: 'select',
            label: '帧索引状态',
            width: 120,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                { label: '正常', value: '正常' },
                { label: '需优化', value: '需优化' },
                { label: '异常', value: '异常' }
              ]
            },
            options: [
              { label: '正常', value: '正常' },
              { label: '需优化', value: '需优化' },
              { label: '异常', value: '异常' }
            ],
            form: {
              value: '正常'
            }
          },
          // 帧存储大小
          value7: {
            type: 'text',
            label: '帧存储大小',
            align: 'right',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 帧处理时间
          value8: {
            type: 'text',
            label: '帧处理时间',
            align: 'center',
            width: 160,
            search: {
              type: 'text',
              placeholder: '请选择帧处理时间'
            }
          }
        }
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val
      },
      immediate: true
    },
    visible(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    // 打开弹窗
    open(rowData) {
      this.currentRowData = rowData
      this.dialogTitle = `帧向量明细 - ${rowData.value2 || '未知视频'}`
      this.visible = true
      // 等待弹窗打开后再加载数据
      this.$nextTick(() => {
        if (this.$refs.sheetRef) {
          this.$refs.sheetRef.getTableData()
        }
      })
    },

    // 关闭弹窗
    close() {
      this.visible = false
    },

    // 弹窗关闭后的处理
    handleClosed() {
      this.currentRowData = null
      this.$emit('closed')
    },

    // 获取帧索引状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '正常': 'success',
        '需优化': 'warning',
        '异常': 'danger'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style scoped>
.frame-detail-sheet {
  height: 60vh;
}

.el-dialog--beautify {
  border-radius: 8px;
}

.el-dialog--beautify .el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
}
</style>
